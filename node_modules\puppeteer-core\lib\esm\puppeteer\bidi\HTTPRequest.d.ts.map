{"version": 3, "file": "HTTPRequest.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/HTTPRequest.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,KAAK,KAAK,IAAI,MAAM,4CAA4C,CAAC;AACxE,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,KAAK,EACV,wBAAwB,EACxB,kBAAkB,EACnB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EACL,WAAW,EAEX,KAAK,YAAY,EAElB,MAAM,uBAAuB,CAAC;AAK/B,OAAO,KAAK,EAAC,OAAO,EAAC,MAAM,mBAAmB,CAAC;AAC/C,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AAEnD,eAAO,MAAM,QAAQ,mCAA0C,CAAC;AAEhE;;GAEG;AACH,qBAAa,eAAgB,SAAQ,WAAW;;IAC9C,MAAM,CAAC,IAAI,CACT,WAAW,EAAE,OAAO,EACpB,KAAK,EAAE,SAAS,EAChB,QAAQ,CAAC,EAAE,eAAe,GACzB,eAAe;IAQlB,SAAkB,EAAE,EAAE,MAAM,CAAC;IAI7B,OAAO;IAgBP,IAAa,MAAM,IAAI,UAAU,CAEhC;IA2CQ,GAAG,IAAI,MAAM;IAIb,YAAY,IAAI,YAAY;IAS5B,MAAM,IAAI,MAAM;IAIhB,QAAQ,IAAI,MAAM,GAAG,SAAS;IAO9B,WAAW,IAAI,OAAO;IAOhB,aAAa,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAmBlD,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAYjC,QAAQ,IAAI,gBAAgB,GAAG,IAAI;IAInC,OAAO,IAAI;QAAC,SAAS,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI;IAOrC,mBAAmB,IAAI,OAAO;IAI9B,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS;IAOnD,aAAa,IAAI,eAAe,EAAE;IAIlC,KAAK,IAAI,SAAS;IAIZ,QAAQ,CACrB,SAAS,CAAC,EAAE,wBAAwB,EACpC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,GAC5B,OAAO,CAAC,IAAI,CAAC;IAUD,SAAS,CACtB,SAAS,GAAE,wBAA6B,GACvC,OAAO,CAAC,IAAI,CAAC;IAsBD,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAQvB,QAAQ,CACrB,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,EACrC,SAAS,CAAC,EAAE,MAAM,GACjB,OAAO,CAAC,IAAI,CAAC;IAgFhB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe;CAGvC"}